<?php

require_once 'app/utils/ControllerHelpers.php';

class BetsController extends \ControllerBase
{
    private $UserUtils;

    /**
     * GetSportsBets
     * @return type
     */
    function GetSportsBets()
    {
        $start_time = $this->getMicrotime();

        $permissionName = "View Customer Bet";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $params['limit'] = $data['limit'] ?? false;
        $params['skipCache'] = $data['skip_cache'] ?? false;
        $params['sort'] = $data['sort'] ?? false;

        $msisdn = $data['mobile_number'] ?? false;
        $ip = $data['ip'] ?? false;
        $bet_type = $data['bet_type'] ?? false;
        $selection_type = $data['selection_type'] ?? false;
        $bet_id = $data['bet_id'] ?? false;
        $profile_id = $data['profile_id'] ?? false;
        $bet_reference = $data['bet_reference'] ?? false;
        $total_odd = $data['total_odd'] ?? false;
        $total_games = $data['total_games'] ?? false;
        $bet_attribution = $data['bet_attribution'] ?? false;
        $match_id = $data['match_id'] ?? false; // Added for parent_match_id search

        $stake_amount_min = $data['stake_amount_min'] ?? false;
        $stake_amount_max = $data['stake_amount_max'] ?? false;
        $winning_amount_min = $data['winning_amount_min'] ?? false;
        $winning_amount_max = $data['winning_amount_max'] ?? false;
        $odds_min = $data['odds_min'] ?? false;
        $odds_max = $data['odds_max'] ?? false;
        $selections_min = $data['selections_min'] ?? false;
        $selections_max = $data['selections_max'] ?? false;

        $unsettled = $data['unsettled'] ?? false;
        $status = $data['status'] ?? false;
        $page = $data['page'] ?? false;
        $export = $data['export'] ?? false;
        $start = $data['start'] ?? false;
        $stop = $data['end'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (!$page) {
            $page = 1;
        }

        if (!$params['limit'] || !is_numeric($params['limit'])) {
            $params['limit'] = $this->settings['SelectRecordLimit'];
        }

        if (!in_array($params['skipCache'], [1, 2])) {
            $params['skipCache'] = 1;
        }

        $order_arr = explode("|", $this->cleanStrSQL($params['sort']));
        if (count($order_arr) > 1) {
            $sort = "sports_bet." . $this->cleanStrSQL($order_arr[0]) . "";
            $order = isset($order_arr[1]) ? $order_arr[1] : 'DESC';

            if (!in_array(strtoupper($order), ['ASC', 'DESC'])) {
                $order = 'DESC';
            }
        } else {
            $sort = 'sports_bet.bet_id';
            $order = 'DESC';
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            // Extract authentication headers using helper
            $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
            $authData = ControllerHelpers::extractAuthHeaders($headers);

            // Extract request data using standardized pattern
            $requestFields = ['timestamp'];
            $params = ControllerHelpers::extractRequestData($data, $requestFields);

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            $searchQuery = " WHERE 1";
            $searchParams = [];

            if (!empty($msisdn)) {
                $msisdn = $this->formatMobileNumber($msisdn);
                $searchParams[':mobile'] = $msisdn;
                $searchQuery .= " AND JSON_UNQUOTE(JSON_EXTRACT(sports_bet.extra_data, '$.msisdn')) = :mobile";
            }

            if (!empty($ip)) {
                $searchParams[':ip_address'] = $ip;
                $searchQuery .= " AND JSON_UNQUOTE(JSON_EXTRACT(sports_bet.extra_data, '$.ip')) = :ip_address";
            }

            if ($profile_id) {
                $searchParams[':profile_id'] = $profile_id;
                $searchQuery .= " AND sports_bet.profile_id = :profile_id";
            }

            if (!empty($bet_id)) {
                $searchParams[':bet_id'] = $bet_id;
                $searchQuery .= " AND sports_bet.bet_id = :bet_id";
            }

            if ($bet_type != null) {
                $searchParams[':bet_type'] = $bet_type;
                $searchQuery .= " AND sports_bet.bet_type = :bet_type";
            }

            if ($selection_type) {
                if ($selection_type == '1') {
                    $searchParams[':total_games'] = 1;
                    $searchQuery .= " AND sports_bet.total_games = :total_games";
                } elseif ($selection_type == '2') {
                    $searchParams[':total_games'] = 2;
                    $searchQuery .= " AND sports_bet.total_games >= :total_games";
                }
            }

            if ($bet_reference != null) {
                $searchParams[':bet_reference'] = $bet_reference;
                $searchQuery .= " AND sports_bet.bet_reference = :bet_reference";
            }

            if ($total_odd != null) {
                $searchParams[':total_odd'] = $total_odd;
                $searchQuery .= " AND sports_bet.total_odd = :total_odd";
            }

            if ($total_games != null) {
                $searchParams[':total_games'] = $total_games;
                $searchQuery .= " AND sports_bet.total_games = :total_games";
            }

            if ($bet_attribution != null) {
                $searchParams[':bet_attribution'] = $bet_attribution;
                $searchQuery .= " AND sports_bet.bet_attribution = :bet_attribution";
            }

            if ($stake_amount_min && $stake_amount_max) {
                $searchParams[':stake_amount_min'] = $stake_amount_min;
                $searchParams[':stake_amount_max'] = $stake_amount_max;
                $searchQuery .= " AND sports_bet.bet_amount BETWEEN :stake_amount_min AND :stake_amount_max";
            }

            if ($winning_amount_min && $winning_amount_max) {
                $searchParams[':winning_amount_min'] = $winning_amount_min;
                $searchParams[':winning_amount_max'] = $winning_amount_max;
                $searchQuery .= " AND sports_bet.possible_win BETWEEN :winning_amount_min AND :winning_amount_max";
            }

            if ($odds_min && $odds_max) {
                $searchParams[':odds_min'] = $odds_min;
                $searchParams[':odds_max'] = $odds_max;
                $searchQuery .= " AND sports_bet.total_odd BETWEEN :odds_min AND :odds_max";
            }

            if ($selections_min && $selections_max) {
                $searchParams[':selections_min'] = $selections_min;
                $searchParams[':selections_max'] = $selections_max;
                $searchQuery .= " AND sports_bet.total_games BETWEEN :selections_min AND :selections_max";
            }

            if (is_numeric($status)) {
                if ($status == 0) {
                    $searchQuery .= " AND (sports_bet.status IS NULL OR sports_bet.status = 0)";
                } else {
                    $searchParams[':status'] = $status;
                    $searchQuery .= " AND sports_bet.status = :status";
                }
            }

            if ($unsettled) {
                $searchQuery .= " AND sports_bet.status = 1 AND sports_bet.bet_credit_transaction_id IS NULL";
            }

            // Add match_id search condition
//            if (!empty($match_id)) {
//                $searchQuery .= " AND EXISTS (SELECT 1 FROM sports_bet_slip WHERE sports_bet_slip.bet_id = sports_bet.bet_id
//                          AND sports_bet_slip.parent_match_id = :match_id)";
//                $searchParams[':match_id'] = $match_id;
//            }

            if (!empty($match_id)) {
                $searchQuery .= " AND EXISTS (SELECT 1 FROM sports_bet_slip 
                  WHERE sports_bet_slip.bet_id = sports_bet.bet_id 
                  AND sports_bet_slip.parent_match_id = :match_id)";
                $searchParams[':match_id'] = $match_id;
            }

            if (($stop != null) && ($start != null)) {
                $searchQuery .= " AND sports_bet.created_at BETWEEN :start AND :stop ";
                $searchParams[':start'] = "$start 00:00:00";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif (($stop != null) && ($start == null)) {
                $searchQuery .= " AND sports_bet.created_at <=:stop";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif ($stop == null && $start != null) {
                $searchQuery .= " AND sports_bet.created_at>=:start";
                $searchParams[':start'] = "$start 00:00:00";
            }

            $sorting = $this->tableQueryBuilder($sort, $order, $page, $params['limit']);
            if ($export == 1) {
                $sorting = "";
            }

            $sql = '' . $searchQuery . ' ' . $sorting;

            $query = "SELECT (SELECT COUNT(sports_bet.bet_id) "
                . "FROM sports_bet $searchQuery) as trx_count, "
                . "sports_bet.bet_id, sports_bet.client_id, sports_bet.profile_id, sports_bet.bet_reference, "
                . "sports_bet.bet_type, sports_bet.bet_transaction_id, sports_bet.bet_credit_transaction_id, "
                . "sports_bet.bet_amount, sports_bet.total_games, sports_bet.total_odd, sports_bet.possible_win, "
                . "sports_bet.witholding_tax, sports_bet.excise_tax, sports_bet.bet_attribution, "
                . "sports_bet.browser_details, sports_bet.kra_report, sports_bet.risk_state, sports_bet.processed,"
                . "sports_bet.extra_data, sports_bet.status bet_status, sports_bet.created_at, "
                . "IFNULL(JSON_UNQUOTE(JSON_EXTRACT(sports_bet.extra_data, '$.msisdn')), null) as mobile, "
                . "IFNULL(JSON_UNQUOTE(JSON_EXTRACT(sports_bet.extra_data, '$.ip')), null) as ip_address "
                . "FROM sports_bet "
                . "$sql";

            $results = $this->rawSelect('dbBetsRead', $query, $searchParams);

            $this->infologger->info(__LINE__ . ":" . __CLASS__
                . ": SQL: $query"
                . "| all_markets params:" . json_encode($searchParams)
                . "| results:" . json_encode($results)
            );

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                    . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'Request returned no Customer Bets!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => 'Queried ' . $results[0]['trx_count']
                        . ' Customer Sports Bets successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'],
                        'result' => $results]], false, true);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    function GetSportsBets1()
    {
        $start_time = $this->getMicrotime();

        $permissionName = "View Customer Bet";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . "[" . $this->ipAddress . "] | Request: GetBets: " . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $params['limit'] = $data['limit'] ?? false;
        $params['skipCache'] = $data['skip_cache'] ?? false;
        $params['sort'] = $data['sort'] ?? false;

        $msisdn = $data['mobile_number'] ?? false;
        $ip = $data['ip'] ?? false;
        $bet_type = $data['bet_type'] ?? false;
        $selection_type = $data['selection_type'] ?? false;
        $bet_id = $data['bet_id'] ?? false;
        $profile_id = $data['profile_id'] ?? false;
        $bet_reference = $data['bet_reference'] ?? false;
        $total_odd = $data['total_odd'] ?? false;
        $total_games = $data['total_games'] ?? false;
        $bet_attribution = $data['bet_attribution'] ?? false;

        $stake_amount_min = $data['stake_amount_min'] ?? false;
        $stake_amount_max = $data['stake_amount_max'] ?? false;
        $winning_amount_min = $data['winning_amount_min'] ?? false;
        $winning_amount_max = $data['winning_amount_max'] ?? false;
        $odds_min = $data['odds_min'] ?? false;
        $odds_max = $data['odds_max'] ?? false;
        $selections_min = $data['selections_min'] ?? false;
        $selections_max = $data['selections_max'] ?? false;

        $unsettled = $data['unsettled'] ?? false;

        $status = $data['status'] ?? false;

        $page = $data['page'] ?? false;
        $export = $data['export'] ?? false;
        $start = $data['start'] ?? false;
        $stop = $data['end'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (!$page) {
            $page = 1;
        }

        if (!$params['limit'] || !is_numeric($params['limit'])) {
            $params['limit'] = $this->settings['SelectRecordLimit'];
        }

        if (!in_array($params['skipCache'], [1, 2])) {
            $params['skipCache'] = 1;
        }

        $order_arr = explode("|", $this->cleanStrSQL($params['sort']));
        if (count($order_arr) > 1) {
            $sort = "sports_bet." . $this->cleanStrSQL($order_arr[0]) . "";
            $order = isset($order_arr[1]) ? $order_arr[1] : 'DESC';

            if (!in_array(strtoupper($order), ['ASC', 'DESC'])) {
                $order = 'DESC';
            }
        } else {
            $sort = 'sports_bet.bet_id';
            $order = 'DESC';
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            // Extract authentication headers using helper
            $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
            $authData = ControllerHelpers::extractAuthHeaders($headers);

            // Extract request data using standardized pattern
            $requestFields = ['timestamp'];
            $params = ControllerHelpers::extractRequestData($data, $requestFields);

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            $searchQuery = " WHERE 1";
            $searchParams = [];

            if (!empty($msisdn)) {
                $msisdn = $this->formatMobileNumber($msisdn);
                $searchParams[':mobile'] = $msisdn;
                $searchQuery .= " AND JSON_UNQUOTE(JSON_EXTRACT(sports_bet.extra_data, '$.msisdn')) = :mobile";
            }

            if (!empty($ip)) {
                $searchParams[':ip_address'] = $ip;
                $searchQuery .= " AND JSON_UNQUOTE(JSON_EXTRACT(sports_bet.extra_data, '$.ip')) = :ip_address";
            }

            if ($profile_id) {
                $searchParams[':profile_id'] = $profile_id;
                $searchQuery .= " AND sports_bet.profile_id = :profile_id";
            }

            if (!empty($bet_id)) {
                $searchParams[':bet_id'] = $bet_id;
                $searchQuery .= " AND sports_bet.bet_id = :bet_id";
            }

            if ($bet_type != null) {
                $searchParams[':bet_type'] = $bet_type;
                $searchQuery .= " AND sports_bet.bet_type = :bet_type";
            }

            if ($selection_type) {
                if ($selection_type == '1') {
                    $searchParams[':total_games'] = 1;
                    $searchQuery .= " AND sports_bet.total_games = :total_games";
                } elseif ($selection_type == '2') {
                    $searchParams[':total_games'] = 2;
                    $searchQuery .= " AND sports_bet.total_games >= :total_games";
                }
            }

            if ($bet_reference != null) {
                $searchParams[':bet_reference'] = $bet_reference;
                $searchQuery .= " AND sports_bet.bet_reference = :bet_reference";
            }

            if ($total_odd != null) {
                $searchParams[':total_odd'] = $total_odd;
                $searchQuery .= " AND sports_bet.total_odd = :total_odd";
            }

            if ($total_games != null) {
                $searchParams[':total_games'] = $total_games;
                $searchQuery .= " AND sports_bet.total_games = :total_games";
            }

            if ($bet_attribution != null) {
                $searchParams[':bet_attribution'] = $bet_attribution;
                $searchQuery .= " AND sports_bet.bet_attribution = :bet_attribution";
            }

            if ($stake_amount_min && $stake_amount_max) {
                $searchParams[':stake_amount_min'] = $stake_amount_min;
                $searchParams[':stake_amount_max'] = $stake_amount_max;
                $searchQuery .= " AND sports_bet.bet_amount BETWEEN :stake_amount_min AND :stake_amount_max";
            }

            if ($winning_amount_min && $winning_amount_max) {
                $searchParams[':winning_amount_min'] = $winning_amount_min;
                $searchParams[':winning_amount_max'] = $winning_amount_max;
                $searchQuery .= " AND sports_bet.possible_win BETWEEN :winning_amount_min AND :winning_amount_max";
            }

            if ($odds_min && $odds_max) {
                $searchParams[':odds_min'] = $odds_min;
                $searchParams[':odds_max'] = $odds_max;
                $searchQuery .= " AND sports_bet.total_odd BETWEEN :odds_min AND :odds_max";
            }

            if ($selections_min && $selections_max) {
                $searchParams[':selections_min'] = $selections_min;
                $searchParams[':selections_max'] = $selections_max;
                $searchQuery .= " AND sports_bet.total_games BETWEEN :selections_min AND :selections_max";
            }

            if (is_numeric($status)) {
                // if status is 0 search null or 0
                if ($status == 0) {
                    $searchQuery .= " AND (sports_bet.status IS NULL OR sports_bet.status = 0)";
                } else {
                    $searchParams[':status'] = $status;
                    $searchQuery .= " AND sports_bet.status = :status";
                }
            }

            if ($unsettled) {
                $searchQuery .= " AND sports_bet.status = 1 AND sports_bet.bet_credit_transaction_id IS NULL";
            }

            if (($stop != null) && ($start != null)) {
                $searchQuery .= " AND sports_bet.created_at BETWEEN :start AND :stop ";
                $searchParams[':start'] = "$start 00:00:00";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif (($stop != null) && ($start == null)) {
                $searchQuery .= " AND sports_bet.created_at <=:stop";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif ($stop == null && $start != null) {
                $searchQuery .= " AND sports_bet.created_at>=:start";
                $searchParams[':start'] = "$start 00:00:00";
            }

            $sorting = $this->tableQueryBuilder($sort, $order, $page, $params['limit']);
            if ($export == 1) {
                $sorting = "";
            }

            $sql = '' . $searchQuery . ' ' . $sorting;

            $query = "SELECT (SELECT COUNT(sports_bet.bet_id) "
                . "FROM sports_bet $searchQuery) as trx_count, "
                . "sports_bet.bet_id, spo sports_bet.client_id, sports_bet.profile_id, sports_bet.bet_reference, "
                . "sports_bet.bet_type, sports_bet.bet_transaction_id, sports_bet.bet_credit_transaction_id, "
                . "sports_bet.bet_amount, sports_bet.total_games, sports_bet.total_odd, sports_bet.possible_win, "
                . "sports_bet.witholding_tax, sports_bet.excise_tax, sports_bet.bet_attribution, "
                . "sports_bet.browser_details, sports_bet.kra_report, sports_bet.risk_state, sports_bet.processed,"
                . "sports_bet.extra_data, sports_bet.status bet_status, sports_bet.created_at, "
                . "IFNULL(JSON_UNQUOTE(JSON_EXTRACT(sports_bet.extra_data, '$.msisdn')), null) as mobile, "
                . "IFNULL(JSON_UNQUOTE(JSON_EXTRACT(sports_bet.extra_data, '$.ip')), null) as ip_address "
                . "FROM sports_bet "
                . "$sql";

            $results = $this->rawSelect('dbBetsRead', $query, $searchParams);
            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                    . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'Request returned no Customer Bets!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => 'Queried ' . $results[0]['trx_count']
                        . ' Customer Sports Bets successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'],
                        'result' => $results]], false, true);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }

    /**
     * GetSportsBetSlips
     * @return type
     */
    function GetSportsBetSlips()
    {
        $start_time = $this->getMicrotime();

        $permissionName = "View Customer Bet Slips";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . "[" . $this->ipAddress . "] | Request: GetSportsBetSlips: " . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $params['limit'] = $data['limit'] ?? false;
        $params['skipCache'] = $data['skip_cache'] ?? false;
        $params['sort'] = $data['sort'] ?? false;

        $bet_id = $data['bet_id'] ?? false;
        $status = $data['status'] ?? false;

        $page = $data['page'] ?? false;
        $export = $data['export'] ?? false;
        $start = $data['start'] ?? false;
        $stop = $data['end'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (!$page) {
            $page = 1;
        }

        if (!$params['limit'] || !is_numeric($params['limit'])) {
            $params['limit'] = $this->settings['SelectRecordLimit'];
        }

        if (!in_array($params['skipCache'], [1, 2])) {
            $params['skipCache'] = 1;
        }

        $order_arr = explode("|", $this->cleanStrSQL($params['sort']));
        if (count($order_arr) > 1) {
            $sort = "sports_bet_slip." . $this->cleanStrSQL($order_arr[0]) . "";
            $order = isset($order_arr[1]) ? $order_arr[1] : 'DESC';

            if (!in_array(strtoupper($order), ['ASC', 'DESC'])) {
                $order = 'DESC';
            }
        } else {
            $sort = 'sports_bet_slip.bet_id';
            $order = 'DESC';
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            // Extract authentication headers using helper
            $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
            $authData = ControllerHelpers::extractAuthHeaders($headers);

            // Extract request data using standardized pattern
            $requestFields = ['timestamp'];
            $params = ControllerHelpers::extractRequestData($data, $requestFields);

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            $searchQuery = " WHERE 1";
            $searchParams = [];

            if ($bet_id) {
                $searchParams[':bet_id'] = $bet_id;
                $searchQuery .= " AND sports_bet_slip.bet_id = :bet_id";
            }

            if ($status) {
                $searchParams[':status'] = $status;
                $searchQuery .= " AND sports_bet_slip.status = :status";
            }

            if (($stop != null) && ($start != null)) {
                $searchQuery .= " AND sports_bet_slip.created_at BETWEEN :start AND :stop ";
                $searchParams[':start'] = "$start 00:00:00";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif (($stop != null) && ($start == null)) {
                $searchQuery .= " AND sports_bet_slip.created_at <=:stop";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif ($stop == null && $start != null) {
                $searchQuery .= " AND sports_bet_slip.created_at>=:start";
                $searchParams[':start'] = "$start 00:00:00";
            }

            $sorting = $this->tableQueryBuilder($sort, $order, $page, $params['limit']);
            if ($export == 1) {
                $sorting = "";
            }

            $sql = '' . $searchQuery . ' ' . $sorting;

            $query = "SELECT (SELECT COUNT(sports_bet_slip.bet_id) FROM sports_bet_slip  $searchQuery) as trx_count,
       sports_bet_slip.slip_id,sports_bet_slip.bet_id,sports_bet_slip.sport_id,sports_bet_slip.parent_match_id,
       sports_bet_slip.odd_value, sports_bet_slip.pick,sports_bet_slip.outcome_name, sports_bet_slip.pick_name,sports_bet_slip.winning_outcome,
       sports_bet_slip.ht_scores, sports_bet_slip.ft_scores, sports_bet_slip.et_scores, sports_bet_slip.extra_data,
       sports_bet_slip.live_bet,sports_bet_slip.status,sports_bet_slip.resulting_type,sports_bet_slip.start_time, sports_bet_slip.created_at
FROM sports_bet_slip $sql";


            $results = $this->rawSelect('dbBetsRead', $query, $searchParams);
            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                    . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'Request returned no Customer Betslip!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => 'Queried ' . $results[0]['trx_count']
                        . ' Customer ' . ' Betslip successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'],
                        'result' => $results]], false, true);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    /**
     * EditFixture
     * @param type $fixtureId
     * @return type
     */
    function ResettleBet($betId)
    {
        $start = $this->getMicrotime();

        $permissionName = "Resettle Bet";

        $data = (array)$this->request->getJsonRawBody();
//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | Request ResettleBet:" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;
        $timestamp = $data['timestamp'] ?? false;
        $betId = $data['bet_id'] ?? false;


        if (!$Authorization || !$appKey || !$hashKey || !$accessToken || !$timestamp || !$betId) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            // Extract authentication headers using helper
            $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
            $authData = ControllerHelpers::extractAuthHeaders($headers);

            // Extract request data using standardized pattern
            $requestFields = ['timestamp'];
            $params = ControllerHelpers::extractRequestData($data, $requestFields);

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            $results = $this->rawSelectOneRecord('dbBetsRead', "SELECT status,bet_credit_transaction_id,total_games FROM sports_bet WHERE bet_id=:bet_id", [':bet_id' => $betId]);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => "Bet with Id $betId does not exists!"], true);
            }

            if ($results['status'] != 1) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "Bet with Id $betId is not won!"], true);
            }

            if ($results['bet_credit_transaction_id'] != null) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "Bet with Id $betId is  settled!"], true);
            }

            $slips = $this->rawSelectOneRecord('dbBetsRead', "SELECT count(slip_id) as count FROM sports_bet_slip WHERE bet_id=:bet_id and status=1", [':bet_id' => $betId]);
            $count = $slips['count'] ?? 0;
            if ($count != $results['total_games']) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "Bet with Id $betId is not fully won!"], true);
            }

            $payload = [
                'betId' => (int)$betId,
                'status' => (int)$results['status'],
                'statusDesc' => 'Won ' . $count . '/' . $results['total_games'],
                'ipAddress' => $this->ipAddress,
                'createdBy' => 'Re-ResultingApp',
                'signature' => 'signature',
                'createdDate' => $this->now()
            ];

            // Queue
            $queue = new Queue();
            $res = $queue->ConnectAndPublishToQueue($payload,
                'BET_SETTLEMENT', 'BET_SETTLEMENT', 'BET_SETTLEMENT'
                , null, null, null, null, "/", null);


            if (!$res) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "Failed to Resettle Bet."], true);
            }

            // LogUserActivity
            $log = UserUtils::LogUserActivity([
                'user_id' => $authResponse['data']['user_id'],
                'activity' => strtoupper(__CLASS__) . " => " . strtoupper(__FUNCTION__),
                'request' => $data,
                'created_at' => $this->now(),
            ]);

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => "Successfully updated fixture "]);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    /**
     * GetVirtualBets
     * @return type
     */
    function GetVirtualBets()
    {
        $start_time = $this->getMicrotime();

        $permissionName = "View Customer Virtual Bet";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . "[" . $this->ipAddress . "] | Request: GetVirtualBets: " . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $params['limit'] = $data['limit'] ?? false;
        $params['skipCache'] = $data['skip_cache'] ?? false;
        $params['sort'] = $data['sort'] ?? false;

        $msisdn = $data['mobile_number'] ?? false;

        $bet_type = $data['bet_type'] ?? false;
        $selection_type = $data['selection_type'] ?? false;
        $bet_id = $data['bet_id'] ?? false;
        $game_id = $data['game_id'] ?? false;
        $game_name = $data['game_name'] ?? false;
        $profile_id = $data['profile_id'] ?? false;
        $bet_reference = $data['bet_reference'] ?? false;
        $total_odd = $data['total_odd'] ?? false;
        $total_games = $data['total_games'] ?? false;
        $bet_attribution = $data['bet_attribution'] ?? false;

        $stake_amount_min = $data['stake_amount_min'] ?? false;
        $stake_amount_max = $data['stake_amount_max'] ?? false;
        $winning_amount_min = $data['winning_amount_min'] ?? false;
        $winning_amount_max = $data['winning_amount_max'] ?? false;
        $odds_min = $data['odds_min'] ?? false;
        $odds_max = $data['odds_max'] ?? false;
        $selections_min = $data['selections_min'] ?? false;
        $selections_max = $data['selections_max'] ?? false;

        $status = $data['status'] ?? false;

        $page = $data['page'] ?? false;
        $export = $data['export'] ?? false;
        $start = $data['start'] ?? false;
        $stop = $data['end'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (!$page) {
            $page = 1;
        }

        if (!$params['limit'] || !is_numeric($params['limit'])) {
            $params['limit'] = $this->settings['SelectRecordLimit'];
        }

        if (!in_array($params['skipCache'], [1, 2])) {
            $params['skipCache'] = 1;
        }

        $order_arr = explode("|", $this->cleanStrSQL($params['sort']));
        if (count($order_arr) > 1) {
            $sort = "v." . $this->cleanStrSQL($order_arr[0]) . "";
            $order = isset($order_arr[1]) ? $order_arr[1] : 'DESC';

            if (!in_array(strtoupper($order), ['ASC', 'DESC'])) {
                $order = 'DESC';
            }
        } else {
            $sort = 'v.bet_id';
            $order = 'DESC';
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            // Extract authentication headers using helper
            $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
            $authData = ControllerHelpers::extractAuthHeaders($headers);

            // Extract request data using standardized pattern
            $requestFields = ['timestamp'];
            $params = ControllerHelpers::extractRequestData($data, $requestFields);

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            $searchQuery = " WHERE 1";
            $searchParams = [];

            // Step 1: Check if MSISDN is provided and query dbProfile
            if ($msisdn) {
                $formattedMsisdn = $this->formatMobileNumber($msisdn);

                // Prepare query to fetch profile details
                $profileQuery = "SELECT id, name FROM profile WHERE msisdn = :msisdn LIMIT 1";
                $profileParams = [':msisdn' => $formattedMsisdn];

                // Execute query
                $profile = $this->rawSelect('dbProfile', $profileQuery, $profileParams);

                if ($profile) {
                    // Extract profile details
                    $profileId = $profile[0]['id'];
                    $profileName = $profile[0]['name'];

                    // Add profile_id to searchParams
                    $searchParams[':profile_id'] = $profileId;
                    $searchQuery .= " AND v.profile_id = :profile_id";
                } else {
                    // Handle case where MSISDN doesn't exist
                    throw new Exception("Profile not found for MSISDN: $formattedMsisdn");
                }
            }

            if ($profile_id) {
                $searchParams[':profile_id'] = $profile_id;
                $searchQuery .= " AND v.profile_id = :profile_id";
            }

            if ($bet_id) {
                $searchParams[':bet_id'] = $bet_id;
                $searchQuery .= " AND v.bet_id = :bet_id";
            }

            if ($game_id) {
                $searchParams[':game_id'] = $game_id;
                $searchQuery .= " AND v.game_id = :game_id";
            }

            if ($game_name) {
                $searchParams[':game_name'] = strtolower($game_name);
                $searchQuery .= " AND LOWER(v.created_by) LIKE CONCAT('%', :game_name, '%')";
            }

            if ($bet_type != null) {
                $searchParams[':bet_type'] = $bet_type;
                $searchQuery .= " AND v.bet_type = :bet_type";
            }

            if ($selection_type) {
                if ($selection_type == '1') {
                    $searchParams[':total_games'] = 1;
                    $searchQuery .= " AND v.total_games = :total_games";
                } elseif ($selection_type == '2') {
                    $searchParams[':total_games'] = 2;
                    $searchQuery .= " AND v.total_games >= :total_games";
                }
            }

            if ($bet_reference != null) {
                $searchParams[':bet_reference'] = $bet_reference;
                $searchQuery .= " AND v.bet_reference = :bet_reference";
            }

            if ($total_odd != null) {
                $searchParams[':total_odd'] = $total_odd;
                $searchQuery .= " AND v.total_odd = :total_odd";
            }

            if ($total_games != null) {
                $searchParams[':total_games'] = $total_games;
                $searchQuery .= " AND v.total_games = :total_games";
            }

            if ($stake_amount_min && $stake_amount_max) {
                $searchParams[':stake_amount_min'] = $stake_amount_min;
                $searchParams[':stake_amount_max'] = $stake_amount_max;
                $searchQuery .= " AND v.bet_amount BETWEEN :stake_amount_min AND :stake_amount_max";
            }

            if ($winning_amount_min && $winning_amount_max) {
                $searchParams[':winning_amount_min'] = $winning_amount_min;
                $searchParams[':winning_amount_max'] = $winning_amount_max;
                $searchQuery .= " AND v.possible_win BETWEEN :winning_amount_min AND :winning_amount_max";
            }

            if ($odds_min && $odds_max) {
                $searchParams[':odds_min'] = $odds_min;
                $searchParams[':odds_max'] = $odds_max;
                $searchQuery .= " AND v.total_odd BETWEEN :odds_min AND :odds_max";
            }

            if ($selections_min && $selections_max) {
                $searchParams[':selections_min'] = $selections_min;
                $searchParams[':selections_max'] = $selections_max;
                $searchQuery .= " AND v.total_games BETWEEN :selections_min AND :selections_max";
            }

            if (is_numeric($status)) {
                $searchParams[':status'] = $status;
                $searchQuery .= " AND v.status = :status";
            }

            if (($stop != null) && ($start != null)) {
                $searchQuery .= " AND v.created_at BETWEEN :start AND :stop ";
                $searchParams[':start'] = "$start 00:00:00";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif (($stop != null) && ($start == null)) {
                $searchQuery .= " AND v.created_at <=:stop";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif ($stop == null && $start != null) {
                $searchQuery .= " AND v.created_at>=:start";
                $searchParams[':start'] = "$start 00:00:00";
            }

            $sorting = $this->tableQueryBuilder($sort, $order, $page, $params['limit']);
            if ($export == 1) {
                $sorting = "";
            }

            $sql = '' . $searchQuery . ' ' . $sorting;

            $query = "SELECT (SELECT COUNT(v.bet_id) FROM virtuals_bet v $searchQuery) as trx_count,"
                . "v.bet_id, v.client_id, v.profile_id, v.provider_name, v.bet_transaction_id, v.bet_credit_transaction_id, "
                . "v.provider_name, v.bet_reference, v.bet_type,v.bet_amount, v.total_games, v.total_odd, v.possible_win, "
                . "v.witholding_tax, v.excise_tax, v.extra_data, v.created_by, v.kra_report, v.status, v.created_at "
                . "FROM virtuals_bet v $sql";

            $results = $this->rawSelect('dbBetsRead', $query, $searchParams);
            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                    . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'Request returned no Customer Bets!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => 'Queried ' . $results[0]['trx_count']
                        . ' Customer ' . ' Bets successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'],
                        'result' => $results]], false, true);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }

    /**
     * GetCasinoBets
     * @return type
     */
    function GetCasinoBets($typeId)
    {
        $start_time = $this->getMicrotime();

        $permissionName = "View Customer Casino Bet";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . "[" . $this->ipAddress . "] | Request: GetCasinoBets: " . json_encode($data) . $typeId);

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $params['limit'] = $data['limit'] ?? false;
        $params['skipCache'] = $data['skip_cache'] ?? false;
        $params['sort'] = $data['sort'] ?? false;

        $msisdn = $data['mobile_number'] ?? false;

        $bet_type = $data['bet_type'] ?? false;
        $selection_type = $data['selection_type'] ?? false;
        $bet_id = $data['bet_id'] ?? false;
        $game_id = $data['game_id'] ?? false;
        $profile_id = $data['profile_id'] ?? false;
        $bet_reference = $data['bet_reference'] ?? false;
        $total_odd = $data['total_odd'] ?? false;
        $total_games = $data['total_games'] ?? false;
        $bet_attribution = $data['bet_attribution'] ?? false;

        $stake_amount_min = $data['stake_amount_min'] ?? false;
        $stake_amount_max = $data['stake_amount_max'] ?? false;
        $winning_amount_min = $data['winning_amount_min'] ?? false;
        $winning_amount_max = $data['winning_amount_max'] ?? false;
        $odds_min = $data['odds_min'] ?? false;
        $odds_max = $data['odds_max'] ?? false;
        $selections_min = $data['selections_min'] ?? false;
        $selections_max = $data['selections_max'] ?? false;

        $status = $data['status'] ?? false;

        $page = $data['page'] ?? false;
        $export = $data['export'] ?? false;
        $start = $data['start'] ?? false;
        $stop = $data['end'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken || !$typeId) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (!$page) {
            $page = 1;
        }

        if (!$params['limit'] || !is_numeric($params['limit'])) {
            $params['limit'] = $this->settings['SelectRecordLimit'];
        }

        if (!in_array($params['skipCache'], [1, 2])) {
            $params['skipCache'] = 1;
        }

        $order_arr = explode("|", $this->cleanStrSQL($params['sort']));
        if (count($order_arr) > 1) {
            $sort = "virtuals_bet." . $this->cleanStrSQL($order_arr[0]) . "";
            $order = isset($order_arr[1]) ? $order_arr[1] : 'DESC';

            if (!in_array(strtoupper($order), ['ASC', 'DESC'])) {
                $order = 'DESC';
            }
        } else {
            $sort = 'virtuals_bet.bet_id';
            $order = 'DESC';
        }

        if (!in_array($typeId, ['sports', 'virtuals', 'instant', 'casino'])) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 421,
                    'message' => "Invalid bet type!"], true);
        }

        $permissionName .= " $typeId";

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            // Extract authentication headers using helper
            $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
            $authData = ControllerHelpers::extractAuthHeaders($headers);

            // Extract request data using standardized pattern
            $requestFields = ['timestamp'];
            $params = ControllerHelpers::extractRequestData($data, $requestFields);

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            $searchQuery = " WHERE 1";
            $searchParams = [];

            // Step 1: Check if MSISDN is provided and query dbProfile
            if ($msisdn) {
                $formattedMsisdn = $this->formatMobformatMobileNumberileNumber($msisdn);

                // Prepare query to fetch profile details
                $profileQuery = "SELECT id, name FROM profile WHERE msisdn = :msisdn LIMIT 1";
                $profileParams = [':msisdn' => $formattedMsisdn];

                // Execute query
                $profile = $this->rawSelect('dbProfile', $profileQuery, $profileParams);

                if ($profile) {
                    // Extract profile details
                    $profileId = $profile[0]['id'];
                    $profileName = $profile[0]['name'];

                    // Add profile_id to searchParams
                    $searchParams[':profile_id'] = $profileId;
                    $searchQuery .= " AND virtuals_bet.profile_id = :profile_id";
                } else {
                    // Handle case where MSISDN doesn't exist
                    throw new Exception("Profile not found for MSISDN: $formattedMsisdn");
                }
            }

            if ($profile_id) {
                $searchParams[':profile_id'] = $profile_id;
                $searchQuery .= " AND virtuals_bet.profile_id = :profile_id";
            }

            if ($bet_id) {
                $searchParams[':bet_id'] = $bet_id;
                $searchQuery .= " AND virtuals_bet.bet_id = :bet_id";
            }

            if ($game_id) {
                $searchParams[':game_id'] = $game_id;
                $searchQuery .= " AND virtuals_bet.game_id = :game_id";
            }

            if ($bet_type != null) {
                $searchParams[':bet_type'] = $bet_type;
                $searchQuery .= " AND virtuals_bet.bet_type = :bet_type";
            }

            if ($selection_type) {
                if ($selection_type == '1') {
                    $searchParams[':total_games'] = 1;
                    $searchQuery .= " AND virtuals_bet.total_games = :total_games";
                } elseif ($selection_type == '2') {
                    $searchParams[':total_games'] = 2;
                    $searchQuery .= " AND virtuals_bet.total_games >= :total_games";
                }
            }

            if ($bet_reference != null) {
                $searchParams[':bet_reference'] = $bet_reference;
                $searchQuery .= " AND virtuals_bet.bet_reference = :bet_reference";
            }

            if ($total_odd != null) {
                $searchParams[':total_odd'] = $total_odd;
                $searchQuery .= " AND virtuals_bet.total_odd = :total_odd";
            }

            if ($total_games != null) {
                $searchParams[':total_games'] = $total_games;
                $searchQuery .= " AND virtuals_bet.total_games = :total_games";
            }

//            if ($bet_attribution != null) {
//                $searchParams[':bet_attribution'] = $bet_attribution;
//                $searchQuery .= " AND virtuals_bet.bet_attribution = :bet_attribution";
//            }

            if ($stake_amount_min && $stake_amount_max) {
                $searchParams[':stake_amount_min'] = $stake_amount_min;
                $searchParams[':stake_amount_max'] = $stake_amount_max;
                $searchQuery .= " AND virtuals_bet.bet_amount BETWEEN :stake_amount_min AND :stake_amount_max";
            }

            if ($winning_amount_min && $winning_amount_max) {
                $searchParams[':winning_amount_min'] = $winning_amount_min;
                $searchParams[':winning_amount_max'] = $winning_amount_max;
                $searchQuery .= " AND virtuals_bet.possible_win BETWEEN :winning_amount_min AND :winning_amount_max";
            }

            if ($odds_min && $odds_max) {
                $searchParams[':odds_min'] = $odds_min;
                $searchParams[':odds_max'] = $odds_max;
                $searchQuery .= " AND virtuals_bet.total_odd BETWEEN :odds_min AND :odds_max";
            }

            if ($selections_min && $selections_max) {
                $searchParams[':selections_min'] = $selections_min;
                $searchParams[':selections_max'] = $selections_max;
                $searchQuery .= " AND virtuals_bet.total_games BETWEEN :selections_min AND :selections_max";
            }

            if ($status) {
                $searchParams[':status'] = $status;
                $searchQuery .= " AND virtuals_bet.status = :status";
            }

            if (($stop != null) && ($start != null)) {
                $searchQuery .= " AND virtuals_bet.created_at BETWEEN :start AND :stop ";
                $searchParams[':start'] = "$start 00:00:00";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif (($stop != null) && ($start == null)) {
                $searchQuery .= " AND virtuals_bet.created_at <=:stop";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif ($stop == null && $start != null) {
                $searchQuery .= " AND virtuals_bet.created_at>=:start";
                $searchParams[':start'] = "$start 00:00:00";
            }

            $sorting = $this->tableQueryBuilder($sort, $order, $page, $params['limit']);
            if ($export == 1) {
                $sorting = "";
            }

            $sql = '' . $searchQuery . ' ' . $sorting;

            $query = "SELECT (SELECT COUNT(virtuals_bet.bet_id) FROM virtuals_bet  $searchQuery) as trx_count, virtuals_bet.bet_id,
       virtuals_bet.client_id,virtuals_bet.profile_id, virtuals_bet.bet_reference, virtuals_bet.bet_type, virtuals_bet.total_games,
       virtuals_bet.bet_attribution,virtuals_bet.status,virtuals_bet.extra_data,virtuals_bet.browser_details, virtuals_bet.kra_report, virtuals_bet.risk_state,
       virtuals_bet.processed, virtuals_bet.created_at FROM virtuals_bet $sql";

            $results = $this->rawSelect('dbBetsRead', $query, $searchParams);
            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                    . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'Request returned no Customer Bets!'], true);
            }

            // Extract all profile IDs from results
            $profileIds = array_column($results, 'profile_id');

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => 'Queried ' . $results[0]['trx_count']
                        . ' Customer ' . $typeId . ' Bets successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'],
                        'result' => $results]], false, true);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    /**
     * GetSoftGamingBets
     * @return type
     */
    function GetSoftGamingBets()
    {
        $start_time = $this->getMicrotime();

        $permissionName = "View Customer Soft Gaming Bet";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . "[" . $this->ipAddress . "] | Request: GetSoftGamingBets: " . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;

        $bet_id = $data['bet_id'] ?? false;
        $bet_reference = $data['bet_reference'] ?? false;
        $customer = $data['customer'] ?? false;
        $ip_address = $data['ip_address'] ?? false;
        $created_by = $data['created_by'] ?? false;
        $min_bet_amount = $data['min_bet_amount'] ?? false;
        $max_bet_amount = $data['max_bet_amount'] ?? false;
        $min_total_odd = $data['min_total_odd'] ?? false;
        $max_total_odd = $data['max_total_odd'] ?? false;
        $min_possible_win = $data['min_possible_win'] ?? false;
        $max_possible_win = $data['max_possible_win'] ?? false;
        $status = $data['status'] ?? false;

        $params['limit'] = $data['limit'] ?? false;
        $params['skipCache'] = $data['skip_cache'] ?? false;
        $params['sort'] = $data['sort'] ?? false;

        $page = $data['page'] ?? false;
        $export = $data['export'] ?? false;
        $start = $data['start_date'] ?? false;
        $stop = $data['end_date'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (!$page) {
            $page = 1;
        }

        if (!$params['limit'] || !is_numeric($params['limit'])) {
            $params['limit'] = $this->settings['SelectRecordLimit'];
        }

        if (!in_array($params['skipCache'], [1, 2])) {
            $params['skipCache'] = 1;
        }

        $order_arr = explode("|", $this->cleanStrSQL($params['sort']));
        if (count($order_arr) > 1) {
            $sort = "softgaming_bets." . $this->cleanStrSQL($order_arr[0]) . "";
            $order = isset($order_arr[1]) ? $order_arr[1] : 'DESC';

            if (!in_array(strtoupper($order), ['ASC', 'DESC'])) {
                $order = 'DESC';
            }
        } else {
            $sort = 'softgaming_bets.bet_id';
            $order = 'DESC';
        }
        try {

            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            // Extract authentication headers using helper
            $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
            $authData = ControllerHelpers::extractAuthHeaders($headers);

            // Extract request data using standardized pattern
            $requestFields = ['timestamp'];
            $params = ControllerHelpers::extractRequestData($data, $requestFields);

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            $searchQuery = " WHERE 1";
            $searchParams = [];


            if (($stop != null) && ($start != null)) {
                $searchQuery .= " AND softgaming_bets.created_at BETWEEN :start AND :stop ";
                $searchParams[':start'] = "$start 00:00:00";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif (($stop != null) && ($start == null)) {
                $searchQuery .= " AND softgaming_bets.created_at <=:stop";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif ($stop == null && $start != null) {
                $searchQuery .= " AND softgaming_bets.created_at>=:start";
                $searchParams[':start'] = "$start 00:00:00";
            }

            if ($bet_id) {
                $searchParams[':bet_id'] = $bet_id;
                $searchQuery .= " AND softgaming_bets.bet_id = :bet_id";
            }

            if ($bet_reference) {
                $searchParams[':bet_reference'] = $bet_reference;
                $searchQuery .= " AND softgaming_bets.bet_reference = :bet_reference";
            }

            if ($customer) {
                $searchParams[':customer'] = $customer;
                $searchQuery .= " AND p.msisdn = :customer";
            }
            if ($ip_address) {
                $searchParams[':ip_address'] = $ip_address;
                $searchQuery .= " AND p.ip_address = :ip_address";
            }

            if ($created_by) {
                $searchParams[':created_by'] = $created_by;
                // use is like
                $searchQuery .= " AND softgaming_bets.created_by LIKE CONCAT('%', :created_by, '%')";
            }

            if ($min_bet_amount) {
                $searchParams[':min_bet_amount'] = (float)$min_bet_amount;
                $searchQuery .= " AND softgaming_bets.bet_amount >= :min_bet_amount";
            }
            if ($max_bet_amount) {
                $searchParams[':max_bet_amount'] = (float)$max_bet_amount;
                $searchQuery .= " AND softgaming_bets.bet_amount <= :max_bet_amount";
            }

            if ($min_total_odd) {
                $searchParams[':min_total_odd'] = $min_total_odd;
                $searchQuery .= " AND softgaming_bets.total_odd >= :min_total_odd";
            }
            if ($max_total_odd) {
                $searchParams[':max_total_odd'] = $max_total_odd;
                $searchQuery .= " AND softgaming_bets.total_odd <= :max_total_odd";
            }

            if ($max_possible_win) {
                $searchParams[':max_possible_win'] = $max_possible_win;
                $searchQuery .= " AND softgaming_bets.possible_win <= :max_possible_win";
            }
            if ($min_possible_win) {
                $searchParams[':min_possible_win'] = $min_possible_win;
                $searchQuery .= " AND softgaming_bets.possible_win >= :min_possible_win";
            }

            if ($status) {
                $searchParams[':status'] = $status;
                $searchQuery .= " AND softgaming_bets.status = :status";
            }

            $sorting = $this->tableQueryBuilder($sort, $order, $page, $params['limit']);
            if ($export == 1) {
                $sorting = "";
            }

            $sql = '' . $searchQuery . ' ' . $sorting;
            $joins = " JOIN mossbets_profile.profile p ON softgaming_bets.profile_id = p.id ";

            $query = "SELECT (SELECT COUNT(softgaming_bets.bet_id) FROM softgaming_bets $joins $searchQuery) as trx_count,"
                . "softgaming_bets.bet_id, softgaming_bets.bet_reference, softgaming_bets.client_id, p.msisdn, "
                . "p.name AS customer, p.ip_address, softgaming_bets.profile_id, softgaming_bets.provider_name, "
                . "softgaming_bets.game_id, softgaming_bets.created_by, softgaming_bets.internal_game_id, "
                . "softgaming_bets.bet_currency, softgaming_bets.bet_amount, softgaming_bets.bet_transaction_id, "
                . "softgaming_bets.bet_credit_transaction_id, softgaming_bets.total_games, softgaming_bets.total_odd, "
                . "softgaming_bets.possible_win, softgaming_bets.bet_type, softgaming_bets.excise_tax, "
                . "softgaming_bets.witholding_tax, softgaming_bets.extra_data, softgaming_bets.kra_report, "
                . "softgaming_bets.status, softgaming_bets.created_at "
                . "FROM softgaming_bets $joins $sql";

            $results = $this->rawSelect('dbBetsRead', $query, $searchParams);
            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                    . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'Request returned no Customer Bets!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => 'Queried ' . $results[0]['trx_count']
                        . ' Customer Soft Gaming Bets successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'],
                        'result' => $results]], false, true);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    /**
     * GetBetAudits
     * @return type
     */
    function GetBetAudits()
    {
        $start_time = $this->getMicrotime();

        $permissionName = "View Bet Audits";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . "[" . $this->ipAddress . "] | Request: GetBetAudits: " . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $params['limit'] = $data['limit'] ?? false;
        $params['skipCache'] = $data['skip_cache'] ?? false;
        $params['sort'] = $data['sort'] ?? false;

        $page = $data['page'] ?? false;
        $export = $data['export'] ?? false;
        $start = $data['start'] ?? false;
        $stop = $data['end'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (!$page) {
            $page = 1;
        }

        if (!$params['limit'] || !is_numeric($params['limit'])) {
            $params['limit'] = $this->settings['SelectRecordLimit'];
        }

        if (!in_array($params['skipCache'], [1, 2])) {
            $params['skipCache'] = 1;
        }

        $order_arr = explode("|", $this->cleanStrSQL($params['sort']));
        if (count($order_arr) > 1) {
            $sort = "bets_audits." . $this->cleanStrSQL($order_arr[0]) . "";
            $order = $order_arr[1] ?? 'DESC';

            if (!in_array(strtoupper($order), ['ASC', 'DESC'])) {
                $order = 'DESC';
            }
        } else {
            $sort = 'bets_audits.id';
            $order = 'DESC';
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            // Extract authentication headers using helper
            $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
            $authData = ControllerHelpers::extractAuthHeaders($headers);

            // Extract request data using standardized pattern
            $requestFields = ['timestamp'];
            $params = ControllerHelpers::extractRequestData($data, $requestFields);

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            $searchQuery = " WHERE 1";
            $searchParams = [];


            if (($stop != null) && ($start != null)) {
                $searchQuery .= " AND bets_audits.created_at BETWEEN :start AND :stop ";
                $searchParams[':start'] = "$start 00:00:00";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif (($stop != null) && ($start == null)) {
                $searchQuery .= " AND bets_audits.created_at <=:stop";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif ($stop == null && $start != null) {
                $searchQuery .= " AND bets_audits.created_at>=:start";
                $searchParams[':start'] = "$start 00:00:00";
            }

            $sorting = $this->tableQueryBuilder($sort, $order, $page, $params['limit']);
            if ($export == 1) {
                $sorting = "";
            }

            $sql = '' . $searchQuery . ' ' . $sorting;

            $query = "SELECT (SELECT COUNT(bets_audits.id) FROM bets_audits  $searchQuery) as trx_count,"
                . "bets_audits.id, bets_audits.client_id, bets_audits.bet_name, bets_audits.bet_type, bets_audits.status,"
                . " bets_audits.created_by, bets_audits.updated_by, bets_audits.created_at"
                . " FROM bets_audits $sql";

            $results = $this->rawSelect('dbBetsRead', $query, $searchParams);
            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                    . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'Request returned no Bets Audits!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => 'Queried ' . $results[0]['trx_count']
                        . 'Bets Audits successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'],
                        'result' => $results]], false, true);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }

    /**
     * UpdateBetAudit
     * @param type $id
     * @return type
     */
    function UpdateBetAudit($id)
    {
        $start = $this->getMicrotime();

        $permissionName = "Update Bet Audit";

        $data = (array)$this->request->getJsonRawBody();

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | Request UpdateBetAudit:" . json_encode($data) . " Fixture Id: " . $id);

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $status = $data['status'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$accessToken || !$timestamp) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            // Extract authentication headers using helper
            $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
            $authData = ControllerHelpers::extractAuthHeaders($headers);

            // Extract request data using standardized pattern
            $requestFields = ['timestamp'];
            $params = ControllerHelpers::extractRequestData($data, $requestFields);

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            $results = $this->rawSelectOneRecord('dbBetsRead',
                "SELECT bets_audits.id, bets_audits.client_id, bets_audits.bet_name,bets_audits.bet_type, bets_audits.status "
                . "FROM bets_audits",
                [':id' => $id]);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => "Fixture Id does not exists!"], true);
            }

            // Build the SET part of the SQL dynamically
            $fields = [];
            $params = [':id' => $id];

            if (is_numeric($status)) {
                $fields[] = "status = :status";
                $params[":status"] = $status;
            }

            if (count($fields) < 1) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    400, 'No valid fields provided for update', [], true);
            }

            $sql = "UPDATE bets_audits SET " . implode(', ', $fields) . " WHERE id = :id Limit 1";

            if (!$this->rawUpdateWithParams('dbBets', $sql, $params)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful', [
                        'code' => 400,
                        'message' => "Sorry, Update failed for BetAudit ID: $id"
                    ], true);
            }

            // LogUserActivity
            $log = UserUtils::LogUserActivity([
                'user_id' => $authResponse['data']['user_id'],
                'activity' => strtoupper(__CLASS__) . " => " . strtoupper(__FUNCTION__),
                'request' => $data,
                'created_at' => $this->now(),
            ]);

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => "Successfully updated fixture "]);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    /**
     * GetBetLimits
     * @return type
     */
    function GetBetLimits()
    {
        $start_time = $this->getMicrotime();

        $permissionName = "View Bet Limits";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . "[" . $this->ipAddress . "] | Request: GetBetLimits: " . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $params['limit'] = $data['limit'] ?? false;
        $params['skipCache'] = $data['skip_cache'] ?? false;
        $params['sort'] = $data['sort'] ?? false;

        $page = $data['page'] ?? false;
        $export = $data['export'] ?? false;
        $start = $data['start'] ?? false;
        $stop = $data['end'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (!$page) {
            $page = 1;
        }

        if (!$params['limit'] || !is_numeric($params['limit'])) {
            $params['limit'] = $this->settings['SelectRecordLimit'];
        }

        if (!in_array($params['skipCache'], [1, 2])) {
            $params['skipCache'] = 1;
        }

        $order_arr = explode("|", $this->cleanStrSQL($params['sort']));
        if (count($order_arr) > 1) {
            $sort = "bet_limits." . $this->cleanStrSQL($order_arr[0]) . "";
            $order = $order_arr[1] ?? 'DESC';

            if (!in_array(strtoupper($order), ['ASC', 'DESC'])) {
                $order = 'DESC';
            }
        } else {
            $sort = 'bet_limits.id';
            $order = 'DESC';
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            // Extract authentication headers using helper
            $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
            $authData = ControllerHelpers::extractAuthHeaders($headers);

            // Extract request data using standardized pattern
            $requestFields = ['timestamp'];
            $params = ControllerHelpers::extractRequestData($data, $requestFields);

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            $searchQuery = " WHERE 1";
            $searchParams = [];


            if (($stop != null) && ($start != null)) {
                $searchQuery .= " AND bet_limits.created_at BETWEEN :start AND :stop ";
                $searchParams[':start'] = "$start 00:00:00";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif (($stop != null) && ($start == null)) {
                $searchQuery .= " AND bet_limits.created_at <=:stop";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif ($stop == null && $start != null) {
                $searchQuery .= " AND bet_limits.created_at>=:start";
                $searchParams[':start'] = "$start 00:00:00";
            }

            $sorting = $this->tableQueryBuilder($sort, $order, $page, $params['limit']);
            if ($export == 1) {
                $sorting = "";
            }

            $sql = '' . $searchQuery . ' ' . $sorting;

            $query = "SELECT (SELECT COUNT(bet_limits.id) FROM bet_limits  $searchQuery) as trx_count,"
                . "bet_limits.id, bet_limits.client_id, bet_limits.bet_name, bet_limits.currency, bet_limits.max_stake,"
                . " bet_limits.min_stake, bet_limits.max_win, bet_limits.risk_approval_amount,"
                . " bet_limits.description, bet_limits.status, bet_limits.created_at"
                . " FROM bet_limits $sql";

            $results = $this->rawSelect('dbBetsRead', $query, $searchParams);
            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                    . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'Request returned no Bets Limits!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => 'Queried ' . $results[0]['trx_count']
                        . 'Bets Audits successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'],
                        'result' => $results]], false, true);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }

    /**
     * UpdateBetLimit
     * @param type $id
     * @return type
     */
    function UpdateBetLimit($id)
    {
        $start = $this->getMicrotime();

        $permissionName = "Update Bet Limit";

        $data = (array)$this->request->getJsonRawBody();

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | Request UpdateBetLimits:" . json_encode($data) . " Fixture Id: " . $id);

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $min_stake = $data['min_stake'] ?? false;
        $max_stake = $data['max_stake'] ?? false;
        $max_win = $data['max_win'] ?? false;
        $risk_approval_amount = $data['risk_approval_amount'] ?? false;
        $status = $data['status'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$accessToken || !$timestamp) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            // Extract authentication headers using helper
            $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
            $authData = ControllerHelpers::extractAuthHeaders($headers);

            // Extract request data using standardized pattern
            $requestFields = ['timestamp'];
            $params = ControllerHelpers::extractRequestData($data, $requestFields);

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            $results = $this->rawSelectOneRecord('dbBetsRead',
                "SELECT bet_limits.id, bet_limits.client_id, bet_limits.bet_name, bet_limits.status "
                . "FROM bet_limits",
                [':id' => $id]);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => "Fixture Id does not exists!"], true);
            }

            // Build the SET part of the SQL dynamically
            $fields = [];
            $params = [':id' => $id];

            if (is_numeric($status)) {
                $fields[] = "status = :status";
                $params[":status"] = $status;
            }

            if (is_numeric($min_stake)) {
                $fields[] = "min_stake = :min_stake";
                $params[":min_stake"] = $min_stake;
            }

            if (is_numeric($max_stake)) {
                $fields[] = "max_stake = :max_stake";
                $params[":max_stake"] = $max_stake;
            }

            if (is_numeric($max_win)) {
                $fields[] = "max_win = :max_win";
                $params[":max_win"] = $max_win;
            }

            if (is_numeric($risk_approval_amount)) {
                $fields[] = "risk_approval_amount = :risk_approval_amount";
                $params[":risk_approval_amount"] = $risk_approval_amount;
            }

            //updatedAt and updatedBy
            $fields[] = "updated_at = NOW()";
//            $fields[] = "updated_by = :updated_by";
//            $params[":updated_by"] = $authResponse['data']['user_id'];

            if (count($fields) < 1) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    400, 'No valid fields provided for update', [], true);
            }

            $sql = "UPDATE bet_limits SET " . implode(', ', $fields) . " WHERE id = :id Limit 1";

            if (!$this->rawUpdateWithParams('dbBets', $sql, $params)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful', [
                        'code' => 400,
                        'message' => "Sorry, Update failed for BetAudit ID: $id"
                    ], true);
            }

            // LogUserActivity
            UserUtils::LogUserActivity([
                'user_id' => $authResponse['data']['user_id'],
                'activity' => strtoupper(__CLASS__) . " => " . strtoupper(__FUNCTION__),
                'request' => $data,
                'created_at' => $this->now(),
            ]);

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => "Successfully updated fixture "]);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }

    /**
     * GetBetGames
     * @return type
     */
    function GetBetGames()
    {
        $start_time = $this->getMicrotime();

        $permissionName = "View Bet Games";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . "[" . $this->ipAddress . "] | Request: GetBetLimits: " . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $params['limit'] = $data['limit'] ?? false;
        $params['skipCache'] = $data['skip_cache'] ?? false;
        $params['sort'] = $data['sort'] ?? false;

        $page = $data['page'] ?? false;
        $export = $data['export'] ?? false;
        $start = $data['start'] ?? false;
        $stop = $data['end'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (!$page) {
            $page = 1;
        }

        if (!$params['limit'] || !is_numeric($params['limit'])) {
            $params['limit'] = $this->settings['SelectRecordLimit'];
        }

        if (!in_array($params['skipCache'], [1, 2])) {
            $params['skipCache'] = 1;
        }

        $order_arr = explode("|", $this->cleanStrSQL($params['sort']));
        if (count($order_arr) > 1) {
            $sort = "games." . $this->cleanStrSQL($order_arr[0]) . "";
            $order = $order_arr[1] ?? 'DESC';

            if (!in_array(strtoupper($order), ['ASC', 'DESC'])) {
                $order = 'DESC';
            }
        } else {
            $sort = 'games.id';
            $order = 'DESC';
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            // Extract authentication headers using helper
            $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
            $authData = ControllerHelpers::extractAuthHeaders($headers);

            // Extract request data using standardized pattern
            $requestFields = ['timestamp'];
            $params = ControllerHelpers::extractRequestData($data, $requestFields);

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            $searchQuery = " WHERE 1";
            $searchParams = [];


            if (($stop != null) && ($start != null)) {
                $searchQuery .= " AND games.created_at BETWEEN :start AND :stop ";
                $searchParams[':start'] = "$start 00:00:00";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif (($stop != null) && ($start == null)) {
                $searchQuery .= " AND games.created_at <=:stop";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif ($stop == null && $start != null) {
                $searchQuery .= " AND games.created_at>=:start";
                $searchParams[':start'] = "$start 00:00:00";
            }

            $sorting = $this->tableQueryBuilder($sort, $order, $page, $params['limit']);
            if ($export == 1) {
                $sorting = "";
            }

            $sql = '' . $searchQuery . ' ' . $sorting;

            $query = "SELECT (SELECT COUNT(games.id) FROM games  $searchQuery) as trx_count,"
                . "games.id, games.game_name, games.provider, games.ip_address, games.description, games.status, "
                . "games.updated_by, games.created_at "
                . "FROM games $sql";

            $results = $this->rawSelect('dbBetsRead', $query, $searchParams);
            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                    . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'Request returned no Bets Limits!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => 'Queried ' . $results[0]['trx_count']
                        . 'Bets Audits successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'],
                        'result' => $results]], false, true);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }

    /**
     * UpdateBetGame
     * @param type $id
     * @return type
     */
    function UpdateBetGame($id)
    {
        $start = $this->getMicrotime();

        $permissionName = "Update Bet Game";

        $data = (array)$this->request->getJsonRawBody();

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | Request UpdateBetLimits:" . json_encode($data) . " Fixture Id: " . $id);

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $status = $data['status'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$accessToken || !$timestamp ) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            // Extract authentication headers using helper
            $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
            $authData = ControllerHelpers::extractAuthHeaders($headers);

            // Extract request data using standardized pattern
            $requestFields = ['timestamp'];
            $params = ControllerHelpers::extractRequestData($data, $requestFields);

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            $results = $this->rawSelectOneRecord('dbBetsRead',
                "SELECT games.id, games.client_id, games.bet_name,games.bet_type, games.status "
                . "FROM games",
                [':id' => $id]);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => "Fixture Id does not exists!"], true);
            }

            // Build the SET part of the SQL dynamically
            $fields = [];
            $params = [':id' => $id];

            if (is_numeric($status)) {
                $fields[] = "status = :status";
                $params[":status"] = $status;
            }

            if (count($fields) < 1) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    400, 'No valid fields provided for update', [], true);
            }

            $sql = "UPDATE games SET " . implode(', ', $fields) . " WHERE id = :id Limit 1";

            if (!$this->rawUpdateWithParams('dbBets', $sql, $params)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful', [
                        'code' => 400,
                        'message' => "Sorry, Update failed for BetAudit ID: $id"
                    ], true);
            }

            // LogUserActivity
            $log = UserUtils::LogUserActivity([
                'user_id' => $authResponse['data']['user_id'],
                'activity' => strtoupper(__CLASS__) . " => " . strtoupper(__FUNCTION__),
                'request' => $data,
                'created_at' => $this->now(),
            ]);


            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => "Successfully updated fixture "]);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    /**
     * GetBetIPs
     * @return type
     */
    function GetBlockedIPs()
    {
        $start_time = $this->getMicrotime();

        $permissionName = "View Blocked IPs";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . "[" . $this->ipAddress . "] | Request: GetBlockedIPs: " . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $limit = $data['limit'] ?? false;
        $page = $data['page'] ?? false;
        $sort = $data['sort'] ?? false;
        $export = $data['export'] ?? false;
        $status = $data['status'] ?? false;
        $ip = $data['ip'] ?? false;


        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (!$page) {
            $page = 1;
        }

        if (!$limit || !is_numeric($limit)) {
            $limit = $this->settings['SelectRecordLimit'];
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            // Extract authentication headers using helper
            $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
            $authData = ControllerHelpers::extractAuthHeaders($headers);

            // Extract request data using standardized pattern
            $requestFields = ['timestamp'];
            $params = ControllerHelpers::extractRequestData($data, $requestFields);

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            $results = $this->rawSelect('dbTrxnRead',
                "SELECT id, ip_address, blocked_upto_date, status, created_at, updated_at "
                . "FROM risk_blocked_ips",
                []);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'No Bet IPs found!'], true);
            }

            $responseData = [
                'record_count' => count($results),
                'result' => $results
            ];

            // Return the response
            return $this->HttpResponse(
                __LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                [
                    'code' => 200,
                    'message' => 'Bet IPs fetched successfully!',
                    'data' => $responseData
                ],
                false,
                true
            );
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }

    /**
     * UpdateBlockedIP
     * @return type
     */
    function UpdateBlockedIP($id)
    {
        $start_time = $this->getMicrotime();
        $permissionName = "Block/UnBlock IP";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }
//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | Request:" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;
        $timestamp = $data['timestamp'] ?? false;
        $status = $data['status'] ?? false;
        $ip_address = $data['ip_address'] ?? false;
        $blocked_upto_date = $data['blocked_upto_date'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }
        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }
            // Extract authentication headers using helper
            $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
            $authData = ControllerHelpers::extractAuthHeaders($headers);

            // Extract request data using standardized pattern
            $requestFields = ['timestamp'];
            $params = ControllerHelpers::extractRequestData($data, $requestFields);

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }
            $results = $this->rawSelectOneRecord('dbTrxnRead', "SELECT id, ip_address, blocked_upto_date, status, created_at, updated_at FROM risk_blocked_ips", [':id' => $id]);
            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 404, 'message' => "Fixture Id does not exists!"], true);
            }
            $fields = [];
            $params = [':id' => $id];
            if (is_numeric($status)) {
                $fields[] = "status = :status";
                $params[":status"] = $status;
            }
            // $blocked_upto_date to null
            if ($status == 1) {
                $fields[] = "blocked_upto_date = :blocked_upto_date";
                // block for 24 hours
                $params[":blocked_upto_date"] = Date('Y-m-d H:i:s', strtotime('+1 day'));
            }

            if (count($fields) < 1) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    400,
                    'No valid fields provided for update', [], true);
            }

            $sql = "UPDATE risk_blocked_ips SET " . implode(', ', $fields) . " WHERE id = :id Limit 1";

            if (!$this->rawUpdateWithParams('dbTrxn', $sql, $params)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400, 'message' => "Sorry, Update failed for BetAudit ID: $id"], true);
            }

            // LogUserActivity
            $log = UserUtils::LogUserActivity([
                'user_id' => $authResponse['data']['user_id'],
                'activity' => strtoupper(__CLASS__) . " => " . strtoupper(__FUNCTION__),
                'request' => $data,
                'created_at' => $this->now(),
            ]);

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is successful',
                ['code' => 200, 'message' => "Successfully updated bet audit"]);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start_time)
                . " Sec(s)" . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }


    /**
     * GetProfileByIP
     * @return type
     */
    function GetBlockedIPProfiles()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "View Blocked IP Profiles";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | Request:" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;
        $timestamp = $data['timestamp'] ?? false;

        $ip_address = $data['ip'] ?? false;
        $limit = $data['limit'] ?? false;
        $page = $data['page'] ?? false;
        $params['sort'] = $data['sort'] ?? false;
        $export = $data['export'] ?? false;
        $status = $data['status'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        try {
            $order_arr = explode("|", $this->cleanStrSQL($params['sort']));
            if (count($order_arr) > 1) {
                $sort = "sports_bet." . $this->cleanStrSQL($order_arr[0]) . "";
                $order = isset($order_arr[1]) ? $order_arr[1] : 'DESC';

                if (!in_array(strtoupper($order), ['ASC', 'DESC'])) {
                    $order = 'DESC';
                }
            } else {
                $sort = 'sports_bet.bet_id';
                $order = 'DESC';
            }

            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }
            // Extract authentication headers using helper
            $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
            $authData = ControllerHelpers::extractAuthHeaders($headers);

            // Extract request data using standardized pattern
            $requestFields = ['timestamp'];
            $params = ControllerHelpers::extractRequestData($data, $requestFields);

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            // If IP address is provided, fetch unique MSISDNs associated with it
            if ($ip_address) {
                $searchQuery = " WHERE 1";
                $searchParams = [];

                $searchParams[':ip_address'] = $ip_address;
                $searchQuery .= " AND JSON_UNQUOTE(JSON_EXTRACT(sports_bet.extra_data, '$.ip')) = :ip_address";

                $sorting = $this->tableQueryBuilder($sort, $order, $page, $limit);
                if ($export == 1) {
                    $sorting = "";
                }

                $sql = $searchQuery . ' ' . $sorting;

                $query = "SELECT (SELECT COUNT(DISTINCT JSON_UNQUOTE(JSON_EXTRACT(sports_bet.extra_data, '$.msisdn'))) "
                    . "FROM sports_bet "
                    . $searchQuery . ") as trx_count, "
                    . "JSON_UNQUOTE(JSON_EXTRACT(sports_bet.extra_data, '$.msisdn')) as mobile, "
                    . "JSON_UNQUOTE(JSON_EXTRACT(sports_bet.extra_data, '$.ip')) as ip_address, "
                    . "COUNT(sports_bet.bet_id) as bet_count, "
                    . "SUM(sports_bet.bet_amount) as total_stake, "
                    . "SUM(sports_bet.possible_win) as total_possible_win, "
                    . "MAX(sports_bet.created_at) as last_bet_date "
                    . "FROM sports_bet "
                    . $searchQuery . " GROUP BY mobile";

                $results = $this->rawSelect('dbBetsRead', $query . ' ' . $sorting, $searchParams);

                if (!$results) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                        . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                        200,
                        'Request is not successful',
                        ['code' => 404,
                            'message' => 'No MSISDNs found for the provided IP address!'], true);
                }

                $responseData = [
                    'record_count' => $results[0]['trx_count'],
                    'result' => $results
                ];

                return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                    . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200,
                    'Request is successful',
                    ['code' => 200,
                        'message' => 'Found ' . $results[0]['trx_count'] . ' unique MSISDNs with bet statistics for IP address ' . $ip_address,
                        'data' => $responseData
                    ], false, true);
            }
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }

    /**
     * GetPartnerBalance
     * @return type
     */
    function GetPartnerBalance()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "View Partner Balance";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

        try {
            // Extract authentication headers using helper
            $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
            $authData = ControllerHelpers::extractAuthHeaders($headers);

            // Extract request data using standardized pattern
            $requestFields = ['timestamp', 'partner_id', 'status'];
            $params = ControllerHelpers::extractRequestData($data, $requestFields);

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            $userId = $authResult['user_id'];

            // Extract pagination and search parameters
            $pagination = ControllerHelpers::extractPaginationParams($data, $this->settings['SelectRecordLimit']);
            $searchParams = ControllerHelpers::extractSearchParams($data);

            // Build search query
            $searchQuery = "WHERE 1=1";
            $queryParams = [];

            if ($params['partner_id']) {
                $searchQuery .= " AND pb.partner_id = :partner_id";
                $queryParams[':partner_id'] = $params['partner_id'];
            }

            if ($params['status'] !== false) {
                $searchQuery .= " AND pb.status = :status";
                $queryParams[':status'] = $params['status'];
            }

            // Build sorting
            $sorting = $this->tableQueryBuilder($searchParams['sort'], $searchParams['order'],
                $pagination['page'], $pagination['limit']);

            if ($pagination['export'] == 1) {
                $sorting = "";
            }

            // Execute query
            $query = "SELECT (SELECT COUNT(pb.id) FROM partner_balance pb $searchQuery) as trx_count,
                      pb.id, pb.partner_id, p.name as partner_name, pb.balance, pb.bonus,
                      pb.status, pb.updated_at
                      FROM partner_balance pb
                      LEFT JOIN partners p ON pb.partner_id = p.id
                      $searchQuery $sorting";

            $results = $this->rawSelect('dbUser', $query, $queryParams);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200, 'Request is not successful', ['code' => 404, 'message' => 'No partner balance information is currently available.'], true);
            }

            // Format balance data
            foreach ($results as &$result) {
                $result['balance'] = number_format((float)$result['balance'], 2, '.', '');
                $result['bonus'] = number_format((float)$result['bonus'], 2, '.', '');
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful', [
                    'code' => 200,
                    'message' => 'Queried ' . $results[0]['trx_count'] . ' Partner Balance records successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'], 'result' => $results]
                ], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__ . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString() . "| Message:" . $ex->getMessage());

            $errorResponse = ControllerHelpers::buildUserFriendlyErrorResponse(__CLASS__, __LINE__,
                $ex->getMessage(), 'processing', 500, $this->CalculateTAT($start_time));

            return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                'Request is not successful', $errorResponse['error'], true);
        }
    }

    /**
     * GetPartnersBetSlips
     * @return type
     */
    function GetPartnersBetSlips()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "View Partner Bet Slips";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

        try {
            // Extract authentication headers using helper
            $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
            $authData = ControllerHelpers::extractAuthHeaders($headers);

            // Extract request data using standardized pattern
            $requestFields = ['timestamp', 'partner_id', 'bet_id', 'sport_id', 'status', 'live_bet'];
            $params = ControllerHelpers::extractRequestData($data, $requestFields);

            // Validate authentication
            $authValidation = ControllerHelpers::validateAuthParams($authData, ['timestamp']);
            if (!$authValidation['valid']) {
                $errorResponse = ControllerHelpers::buildAuthErrorResponse(__CLASS__, __LINE__, $authValidation['missing_fields']);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            // Authenticate user
            $authResponse = UserUtils::QuickAuthenticate($authData['access_token']);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request Requires Authentication',
                    ['code' => $authResponse['code'], 'message' => $authResponse['message']], true);
            }

            // Check permissions
            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 403, 'message' => "Action require privileged access!"], true);
            }

            // Extract pagination and search parameters
            $pagination = ControllerHelpers::extractPaginationParams($data, $this->settings['SelectRecordLimit']);
            $searchParams = ControllerHelpers::extractSearchParams($data);
            $dateRange = ControllerHelpers::extractDateRange($data);

            // Build search query
            $searchQuery = "WHERE 1=1";
            $queryParams = [];

            if ($params['partner_id']) {
                $searchQuery .= " AND pbs.partner_id = :partner_id";
                $queryParams[':partner_id'] = $params['partner_id'];
            }

            if ($params['bet_id']) {
                $searchQuery .= " AND pbs.bet_id = :bet_id";
                $queryParams[':bet_id'] = $params['bet_id'];
            }

            if ($params['sport_id']) {
                $searchQuery .= " AND pbs.sport_id = :sport_id";
                $queryParams[':sport_id'] = $params['sport_id'];
            }

            if ($params['status'] !== false) {
                $searchQuery .= " AND pbs.status = :status";
                $queryParams[':status'] = $params['status'];
            }

            if ($params['live_bet'] !== false) {
                $searchQuery .= " AND pbs.live_bet = :live_bet";
                $queryParams[':live_bet'] = $params['live_bet'];
            }

            if ($dateRange['start_date']) {
                $searchQuery .= " AND DATE(pbs.created_at) >= :start_date";
                $queryParams[':start_date'] = $dateRange['start_date'];
            }

            if ($dateRange['end_date']) {
                $searchQuery .= " AND DATE(pbs.created_at) <= :end_date";
                $queryParams[':end_date'] = $dateRange['end_date'];
            }

            // Build sorting
            $sorting = $this->tableQueryBuilder($searchParams['sort'], $searchParams['order'],
                $pagination['page'], $pagination['limit']);

            if ($pagination['export'] == 1) {
                $sorting = "";
            }

            // Execute query
            $query = "SELECT (SELECT COUNT(pbs.slip_id) FROM partners_bet_slips pbs $searchQuery) as trx_count,
                      pbs.slip_id, pbs.partner_id, p.name as partner_name, pbs.bet_id, pbs.sport_id,
                      pbs.parent_match_id, pbs.parent_market_id, pbs.market_id, pbs.selection_id,
                      pbs.outcome_name, pbs.odd_value, pbs.pick, pbs.pick_name, pbs.winning_outcome,
                      pbs.ht_scores, pbs.ft_scores, pbs.et_scores, pbs.live_bet, pbs.status,
                      pbs.resulting_type, pbs.start_time, pbs.created_at, pbs.updated_at
                      FROM partners_bet_slips pbs
                      LEFT JOIN partners p ON pbs.partner_id = p.id
                      $searchQuery $sorting";

            $results = $this->rawSelect('dbUser', $query, $queryParams);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200, 'Request is not successful', ['code' => 404, 'message' => 'No partner bet slips found!'], true);
            }

            // Format numeric values
            foreach ($results as &$result) {
                $result['odd_value'] = number_format((float)$result['odd_value'], 2, '.', '');
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful', [
                    'code' => 200,
                    'message' => 'Queried ' . $results[0]['trx_count'] . ' Partner Bet Slips successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'], 'result' => $results]
                ], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__ . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString() . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful', ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }


}
