<?php

/**
 * Description of PartnerController
 *
 * <AUTHOR>
 */
class PartnerController extends \ControllerBase
{

    /**
     * GetPartners
     * @return type
     */
    public function GetPartners()
    {
        $start_time = $this->getMicrotime();

        $permissionName = "View Partners";

        $data = $this->request->get();

        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request GetPartners :" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $params['limit'] = $data['limit'] ?? false;
        $params['skipCache'] = $data['skip_cache'] ?? false;
        $params['sort'] = $data['sort'] ?? false;
        $page = $data['page'] ?? false;
        $export = $data['export'] ?? false;
        $status = $data['status'] ?? false;
        $start = $data['start'] ?? false;
        $stop = $data['end'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (!$page) {
            $page = 1;
        }

        if (!$params['limit'] || !is_numeric($params['limit'])) {
            $params['limit'] = $this->settings['SelectRecordLimit'];
        }

        if (!in_array($params['skipCache'], [1, 2])) {
            $params['skipCache'] = 1;
        }

        $order_arr = explode("|", $this->cleanStrSQL($params['sort']));
        if (count($order_arr) > 1) {
            $sort = "p." . $this->cleanStrSQL($order_arr[0]) . "";
            $order = isset($order_arr[1]) ? $order_arr[1] : 'DESC';

            if (!in_array(strtoupper($order), ['ASC', 'DESC'])) {
                $order = 'DESC';
            }
        } else {
            $sort = 'p.id';
            $order = 'DESC';
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            // Extract authentication headers using helper
            $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
            $authData = ControllerHelpers::extractAuthHeaders($headers);

            // Extract request data using standardized pattern
            $requestFields = ['timestamp'];
            $params = ControllerHelpers::extractRequestData($data, $requestFields);

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            $searchQuery = " WHERE 1";
            $searchParams = [];


            if (is_numeric($status)) {
                $searchParams[':status'] = $status;
                $searchQuery .= " AND p.status = :status";
            }

            if (($stop != null) && ($start != null)) {
                $searchQuery .= " AND p.created_at BETWEEN :start AND :stop ";
                $searchParams[':start'] = "$start 00:00:00";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif (($stop != null) && ($start == null)) {
                $searchQuery .= " AND p.created_at <=:stop";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif ($stop == null && $start != null) {
                $searchQuery .= " AND p.created_at>=:start";
                $searchParams[':start'] = "$start 00:00:00";
            }

            $sorting = $this->tableQueryBuilder($sort, $order, $page, $params['limit']);
            if ($export == 1) {
                $orderBy = $sort ? "ORDER BY $sort $order" : "";
                $exportLimit = 50000;
                $sorting = "$orderBy LIMIT $exportLimit";
            }

            // select id,name,status,created_at,address,country,msisdn from partners;
            $sql = "SELECT (SELECT COUNT(p.id) FROM partners p  $searchQuery) as trx_count,"
                . "p.id, p.name, p.status, p.created_at, p.address, p.country, p.msisdn "
                . "FROM partners p $searchQuery $sorting";

            $results = $this->rawSelect('dbSportsRead', $sql, $searchParams);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                    . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'Request returned no Partners!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => 'Queried ' . $results[0]['trx_count']
                        . ' Partners successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'],
                        'result' => $results]], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    /**
     * CreatePartner
     * @return type
     */
    public function CreatePartner()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "Create Partners";

        $data = $this->request->getJsonRawBody();
        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request CreatePartner :" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $name = $data->name ?? false;
        $status = $data->status ?? 'active';
        $address = $data->address ?? null;
        $country = $data->country ?? null;
        $msisdn = $data->msisdn ?? null;

        if (!$Authorization || !$appKey || !$hashKey || !$accessToken || !$name) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200, 'Request is not successful',
                ['code' => 422, 'message' => "Mandatory field(s) required!"], true);
        }

        // Validate status
        if (!in_array($status, ['active', 'inactive'])) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200, 'Request is not successful',
                ['code' => 422, 'message' => "Invalid status. Must be 'active' or 'inactive'"], true);
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 401, 'message' => 'Authorization Error. Invalid Key!'], true);
            }

            // Extract authentication headers using helper
            $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
            $authData = ControllerHelpers::extractAuthHeaders($headers);

            // Extract request data using standardized pattern
            $requestFields = ['timestamp'];
            $params = ControllerHelpers::extractRequestData($data, $requestFields);

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            // Check if partner name already exists
            $existingPartner = $this->rawSelectOneRecord('dbUser',
                "SELECT id FROM partners WHERE name = :name", [':name' => $name]);

            if ($existingPartner) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 409, 'message' => 'Partner name already exists!'], true);
            }

            // Insert new partner
            $partnerId = $this->rawInsertBulk('dbUser', 'partners', [
                'name' => $name,
                'status' => $status,
                'address' => $address,
                'country' => $country,
                'msisdn' => $msisdn,
                'created_at' => $this->now()
            ]);

            if (!$partnerId) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 500, 'message' => 'Failed to create partner!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful',
                ['code' => 201, 'message' => 'Partner created successfully!',
                    'data' => ['partner_id' => $partnerId]], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful',
                ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

    /**
     * UpdatePartner
     * @return type
     */
    public function UpdatePartner()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "Update Partners";

        $data = $this->request->getJsonRawBody();
        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request UpdatePartner :" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $partnerId = $data->partner_id ?? false;
        $name = $data->name ?? false;
        $status = $data->status ?? false;
        $address = $data->address ?? null;
        $country = $data->country ?? null;
        $msisdn = $data->msisdn ?? null;

        if (!$Authorization || !$appKey || !$hashKey || !$accessToken || !$partnerId) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200, 'Request is not successful',
                ['code' => 422, 'message' => "Mandatory field(s) required!"], true);
        }

        // Validate status if provided
        if ($status && !in_array($status, ['active', 'inactive'])) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200, 'Request is not successful',
                ['code' => 422, 'message' => "Invalid status. Must be 'active' or 'inactive'"], true);
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 401, 'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 401, 'message' => 'Security Error. Wrong authenticated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request Requires Authentication',
                    ['code' => $authResponse['code'], 'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 403, 'message' => "Action require privileged access!"], true);
            }

            // Check if partner exists
            $existingPartner = $this->rawSelectOneRecord('dbUser',
                "SELECT id, name FROM partners WHERE id = :id", [':id' => $partnerId]);

            if (!$existingPartner) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 404, 'message' => 'Partner not found!'], true);
            }

            // Check if new name conflicts with existing partner (if name is being updated)
            if ($name && $name !== $existingPartner['name']) {
                $nameConflict = $this->rawSelectOneRecord('dbUser',
                    "SELECT id FROM partners WHERE name = :name AND id != :id",
                    [':name' => $name, ':id' => $partnerId]);

                if ($nameConflict) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200, 'Request is not successful',
                        ['code' => 409, 'message' => 'Partner name already exists!'], true);
                }
            }

            // Build update query dynamically
            $updateFields = [];
            $updateParams = [':id' => $partnerId];

            if ($name) {
                $updateFields[] = "name = :name";
                $updateParams[':name'] = $name;
            }
            if ($status) {
                $updateFields[] = "status = :status";
                $updateParams[':status'] = $status;
            }
            if ($address !== null) {
                $updateFields[] = "address = :address";
                $updateParams[':address'] = $address;
            }
            if ($country !== null) {
                $updateFields[] = "country = :country";
                $updateParams[':country'] = $country;
            }
            if ($msisdn !== null) {
                $updateFields[] = "msisdn = :msisdn";
                $updateParams[':msisdn'] = $msisdn;
            }

            if (empty($updateFields)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 422, 'message' => 'No fields to update!'], true);
            }

            $updateSql = "UPDATE partners SET " . implode(', ', $updateFields) . " WHERE id = :id";
            $result = $this->rawUpdateWithParams('dbUser', $updateSql, $updateParams);

            if (!$result) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 500, 'message' => 'Failed to update partner!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful',
                ['code' => 200, 'message' => 'Partner updated successfully!'], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful',
                ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

    /**
     * DeletePartner
     * @return type
     */
    public function DeletePartner()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "Delete Partners";

        $data = $this->request->getJsonRawBody();
        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request DeletePartner :" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $partnerId = $data->partner_id ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$accessToken || !$partnerId) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200, 'Request is not successful',
                ['code' => 422, 'message' => "Mandatory field(s) required!"], true);
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 401, 'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 401, 'message' => 'Security Error. Wrong authenticated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request Requires Authentication',
                    ['code' => $authResponse['code'], 'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 403, 'message' => "Action require privileged access!"], true);
            }

            // Check if partner exists
            $existingPartner = $this->rawSelectOneRecord('dbUser',
                "SELECT id FROM partners WHERE id = :id", [':id' => $partnerId]);

            if (!$existingPartner) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 404, 'message' => 'Partner not found!'], true);
            }

            // Check for dependencies (partner services, settings, etc.)
            $dependencies = $this->rawSelectOneRecord('dbUser',
                "SELECT COUNT(*) as count FROM partner_services WHERE partner_id = :id",
                [':id' => $partnerId]);

            if ($dependencies && $dependencies['count'] > 0) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 409, 'message' => 'Cannot delete partner with existing services!'], true);
            }

            // Soft delete by setting status to inactive
            $result = $this->rawUpdateWithParams('dbUser',
                "UPDATE partners SET status = 'inactive' WHERE id = :id",
                [':id' => $partnerId]);

            if (!$result) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 500, 'message' => 'Failed to delete partner!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful',
                ['code' => 200, 'message' => 'Partner deleted successfully!'], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful',
                ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

    /**
     * GetPartnerServices
     * @return type
     */
    public function GetPartnerServices()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "View Partner Services";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request GetPartnerServices :" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $params['limit'] = $data['limit'] ?? false;
        $params['skipCache'] = $data['skip_cache'] ?? false;
        $params['sort'] = $data['sort'] ?? false;
        $page = $data['page'] ?? false;
        $partnerId = $data['partner_id'] ?? false;
        $serviceId = $data['service_id'] ?? false;
        $status = $data['status'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200, 'Request is not successful',
                ['code' => 422, 'message' => "Mandatory field(s) required!"], true);
        }

        if (!$page) $page = 1;
        if (!$params['limit'] || !is_numeric($params['limit'])) {
            $params['limit'] = $this->settings['SelectRecordLimit'];
        }
        if (!in_array($params['skipCache'], [1, 2])) {
            $params['skipCache'] = 1;
        }

        $order_arr = explode("|", $this->cleanStrSQL($params['sort']));
        if (count($order_arr) > 1) {
            $sort = "ps." . $this->cleanStrSQL($order_arr[0]);
            $order = isset($order_arr[1]) ? $order_arr[1] : 'DESC';
            if (!in_array(strtoupper($order), ['ASC', 'DESC'])) {
                $order = 'DESC';
            }
        } else {
            $sort = 'ps.id';
            $order = 'DESC';
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 401, 'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 401, 'message' => 'Security Error. Wrong authenticated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request Requires Authentication',
                    ['code' => $authResponse['code'], 'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 403, 'message' => "Action require privileged access!"], true);
            }

            $searchQuery = " WHERE 1";
            $searchParams = [];

            if (is_numeric($partnerId)) {
                $searchParams[':partner_id'] = $partnerId;
                $searchQuery .= " AND ps.partner_id = :partner_id";
            }

            if (is_numeric($serviceId)) {
                $searchParams[':service_id'] = $serviceId;
                $searchQuery .= " AND ps.service_id = :service_id";
            }

            if ($status) {
                $searchParams[':status'] = $status;
                $searchQuery .= " AND ps.status = :status";
            }

            $sorting = $this->tableQueryBuilder($sort, $order, $page, $params['limit']);

            $sql = "SELECT (SELECT COUNT(ps.id) FROM partner_services ps $searchQuery) as trx_count,"
                . "ps.id, ps.partner_id, ps.service_id, ps.rate_limit_per_minute, ps.status, ps.created_at, "
                . "p.name as partner_name, s.name as service_name "
                . "FROM partner_services ps "
                . "LEFT JOIN partners p ON ps.partner_id = p.id "
                . "LEFT JOIN services s ON ps.service_id = s.id "
                . "$searchQuery $sorting";

            $results = $this->rawSelect('dbUser', $sql, $searchParams);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                    . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200, 'Request is not successful',
                    ['code' => 404, 'message' => 'Request returned no Partner Services!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful',
                ['code' => 200, 'message' => 'Queried ' . $results[0]['trx_count'] . ' Partner Services successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'], 'result' => $results]], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful',
                ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

    /**
     * CreatePartnerService
     * @return type
     */
    public function CreatePartnerService()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "Create Partner Services";

        $data = $this->request->getJsonRawBody();
        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request CreatePartnerService :" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $partnerId = $data->partner_id ?? false;
        $serviceId = $data->service_id ?? false;
        $rateLimitPerMinute = $data->rate_limit_per_minute ?? 60;
        $status = $data->status ?? 'active';

        if (!$Authorization || !$appKey || !$hashKey || !$accessToken || !$partnerId || !$serviceId) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200, 'Request is not successful',
                ['code' => 422, 'message' => "Mandatory field(s) required!"], true);
        }

        // Validate rate limit
        if (!is_numeric($rateLimitPerMinute) || $rateLimitPerMinute < 1 || $rateLimitPerMinute > 1000) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200, 'Request is not successful',
                ['code' => 422, 'message' => "Rate limit must be between 1 and 1000"], true);
        }

        // Validate status
        if (!in_array($status, ['active', 'inactive'])) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200, 'Request is not successful',
                ['code' => 422, 'message' => "Invalid status. Must be 'active' or 'inactive'"], true);
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 401, 'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 401, 'message' => 'Security Error. Wrong authenticated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request Requires Authentication',
                    ['code' => $authResponse['code'], 'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 403, 'message' => "Action require privileged access!"], true);
            }

            // Validate partner exists
            $partner = $this->rawSelectOneRecord('dbUser',
                "SELECT id FROM partners WHERE id = :id AND status = 'active'", [':id' => $partnerId]);

            if (!$partner) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 404, 'message' => 'Partner not found or inactive!'], true);
            }

            // Validate service exists
            $service = $this->rawSelectOneRecord('dbUser',
                "SELECT id FROM services WHERE id = :id", [':id' => $serviceId]);

            if (!$service) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 404, 'message' => 'Service not found!'], true);
            }

            // Check if partner service already exists
            $existingService = $this->rawSelectOneRecord('dbUser',
                "SELECT id FROM partner_services WHERE partner_id = :partner_id AND service_id = :service_id",
                [':partner_id' => $partnerId, ':service_id' => $serviceId]);

            if ($existingService) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 409, 'message' => 'Partner service already exists!'], true);
            }

            // Insert new partner service
            $partnerServiceId = $this->rawInsertBulk('dbUser', 'partner_services', [
                'partner_id' => $partnerId,
                'service_id' => $serviceId,
                'rate_limit_per_minute' => $rateLimitPerMinute,
                'status' => $status,
                'created_at' => $this->now()
            ]);

            if (!$partnerServiceId) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 500, 'message' => 'Failed to create partner service!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful',
                ['code' => 201, 'message' => 'Partner service created successfully!',
                    'data' => ['partner_service_id' => $partnerServiceId]], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful',
                ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

    /**
     * UpdatePartnerService
     * @return type
     */
    public function UpdatePartnerService($partnerServiceId) {
        $start_time = $this->getMicrotime();
        $permissionName = "Update Partner Services";

        $data = $this->request->getJsonRawBody();
        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request UpdatePartnerService :" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $rateLimitPerMinute = $data->rate_limit_per_minute ?? false;
        $status = $data->status ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200, 'Request is not successful',
                ['code' => 422, 'message' => "Mandatory field(s) required!"], true);
        }

        // Validate rate limit if provided
        if ($rateLimitPerMinute !== false && (!is_numeric($rateLimitPerMinute) || $rateLimitPerMinute < 1 || $rateLimitPerMinute > 1000)) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200, 'Request is not successful',
                ['code' => 422, 'message' => "Rate limit must be between 1 and 1000"], true);
        }

        // Validate status if provided
        if ($status !== false && !in_array($status, ['active', 'inactive'])) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200, 'Request is not successful',
                ['code' => 422, 'message' => "Invalid status. Must be 'active' or 'inactive'"], true);
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 401, 'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 401, 'message' => 'Security Error. Wrong authenticated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request Requires Authentication',
                    ['code' => $authResponse['code'], 'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 403, 'message' => "Action require privileged access!"], true);
            }

            // Validate partner service exists
            $partnerService = $this->rawSelectOneRecord('dbUser',
                "SELECT id FROM partner_services WHERE id = :id", [':id' => $partnerServiceId]);

            if (!$partnerService) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 404, 'message' => 'Partner service not found!'], true);
            }

            // Build update query dynamically
            $updateFields = [];
            $updateParams = [':id' => $partnerServiceId];

            if ($rateLimitPerMinute !== null) {
                $updateFields[] = "rate_limit_per_minute = :rate_limit_per_minute";
                $updateParams[':rate_limit_per_minute'] = $rateLimitPerMinute;
            }
            if ($status !== null) {
                $updateFields[] = "status = :status";
                $updateParams[':status'] = $status;
            }

            if (empty($updateFields)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 422, 'message' => 'No fields to update!'], true);
            }

            $updateSql = "UPDATE partner_services SET " . implode(', ', $updateFields) . " WHERE id = :id";
            $result = $this->rawUpdateWithParams('dbUser', $updateSql, $updateParams);

            if (!$result) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 500, 'message' => 'Failed to update partner service!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful',
                ['code' => 200, 'message' => 'Partner service updated successfully!'], false, true);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful',
                ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

}