<?php

require_once 'app/utils/ControllerHelpers.php';

/**
 * Description of DashboardController
 *
 * <AUTHOR>
 */
class DashboardController extends \ControllerBase
{

    /**
     * GetDashboardCharts
     * @return type
     */
    function GetDashboardCharts()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "View Dashboard Charts";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

        try {
            // Extract authentication headers using helper
            $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
            $authData = ControllerHelpers::extractAuthHeaders($headers);

            // Extract request data using standardized pattern
            $requestFields = ['timestamp', 'chart_type', 'period'];
            $params = ControllerHelpers::extractRequestData($data, $requestFields);

            // Validate authentication
            $authValidation = ControllerHelpers::validateAuthParams($authData, ['timestamp']);
            if (!$authValidation['valid']) {
                $errorResponse = ControllerHelpers::buildAuthErrorResponse(__CLASS__, __LINE__, $authValidation['missing_fields']);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            $chartData = [];
            $period = $params['period'] ?: '7days';
            $chartType = $params['chart_type'] ?: 'all';

            // Partner Balance Chart
            if ($chartType === 'all' || $chartType === 'partner_balance') {
                $balanceData = $this->rawSelect('dbUser',
                    "SELECT p.name as partner_name, pb.balance, pb.bonus
                     FROM partner_balance pb
                     JOIN partners p ON pb.partner_id = p.id
                     WHERE pb.status = 1
                     ORDER BY pb.balance DESC
                     LIMIT 10", []);

                if ($balanceData) {
                    $chartData['partner_balance'] = ControllerHelpers::buildChartData($balanceData, 'partner_name', 'balance');
                    $chartData['partner_balance']['title'] = 'Top 10 Partners by Balance';
                    $chartData['partner_balance']['type'] = 'doughnut';
                }
            }

            // Bets by Sport Chart
            if ($chartType === 'all' || $chartType === 'bets_by_sport') {
                $dateCondition = $this->getDateCondition($period);
                $sportsData = $this->rawSelect('dbBetsRead',
                    "SELECT s.sport_name, COUNT(sb.bet_id) as bet_count, SUM(sb.bet_amount) as total_amount
                     FROM sports_bet sb
                     JOIN sports s ON sb.sport_id = s.id
                     WHERE sb.status IN (0,1,2,3) $dateCondition
                     GROUP BY s.sport_name
                     ORDER BY bet_count DESC
                     LIMIT 8", []);

                if ($sportsData) {
                    $chartData['bets_by_sport'] = ControllerHelpers::buildChartData($sportsData, 'sport_name', 'bet_count');
                    $chartData['bets_by_sport']['title'] = 'Bets by Sport (' . ucfirst($period) . ')';
                    $chartData['bets_by_sport']['type'] = 'bar';
                }
            }

            // Revenue Trends Chart
            if ($chartType === 'all' || $chartType === 'revenue_trends') {
                $dateCondition = $this->getDateCondition($period);
                $revenueData = $this->rawSelect('dbTrxnRead',
                    "SELECT DATE(created_at) as date,
                            SUM(CASE WHEN reference_type_id = 2 THEN amount ELSE 0 END) as deposits,
                            SUM(CASE WHEN reference_type_id = 1 THEN amount ELSE 0 END) as withdrawals,
                            SUM(CASE WHEN reference_type_id = 3 THEN amount ELSE 0 END) as stakes
                     FROM transaction_summary
                     WHERE 1=1 $dateCondition
                     GROUP BY DATE(created_at)
                     ORDER BY date DESC
                     LIMIT 30", []);

                if ($revenueData) {
                    $chartData['revenue_trends'] = [
                        'title' => 'Revenue Trends (' . ucfirst($period) . ')',
                        'type' => 'line',
                        'data' => $revenueData
                    ];
                }
            }

            // Partner Performance Chart
            if ($chartType === 'all' || $chartType === 'partner_performance') {
                $dateCondition = $this->getDateCondition($period);
                $partnerData = $this->rawSelect('dbBetsRead',
                    "SELECT p.name as partner_name,
                            COUNT(sb.bet_id) as total_bets,
                            SUM(sb.bet_amount) as total_stakes,
                            SUM(CASE WHEN sb.status = 1 THEN sb.possible_win ELSE 0 END) as total_winnings,
                            (SUM(sb.bet_amount) - SUM(CASE WHEN sb.status = 1 THEN sb.possible_win ELSE 0 END)) as net_revenue
                     FROM sports_bet sb
                     JOIN partners p ON sb.partner_id = p.id
                     WHERE sb.status IN (0,1,2,3) $dateCondition
                     GROUP BY p.id, p.name
                     ORDER BY net_revenue DESC
                     LIMIT 10", []);

                if ($partnerData) {
                    $chartData['partner_performance'] = [
                        'title' => 'Top 10 Partners by Net Revenue (' . ucfirst($period) . ')',
                        'type' => 'bar',
                        'data' => $partnerData
                    ];
                }
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful', [
                    'code' => 200,
                    'message' => 'Dashboard charts data retrieved successfully!',
                    'data' => $chartData,
                    'summary' => [
                        'total_charts' => count($chartData),
                        'period' => $period,
                        'chart_type' => $chartType
                    ]
                ], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__ . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString() . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful', ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

    /**
     * Helper method to get date condition based on period
     * @param string $period
     * @return string
     */
    private function getDateCondition($period)
    {
        $days = $this->getDaysFromPeriod($period);
        return " AND DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL $days DAY)";
    }

    /**
     * Helper method to get number of days from period string
     * @param string $period
     * @return int
     */
    private function getDaysFromPeriod($period)
    {
        switch ($period) {
            case '1day':
                return 1;
            case '7days':
                return 7;
            case '30days':
                return 30;
            case '90days':
                return 90;
            default:
                return 7;
        }
    }

}
